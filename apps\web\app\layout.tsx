import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

import "@workspace/ui/styles/globals.css";

import { Providers } from "@/components/providers";

const fontSans = Geist({
  subsets: ["latin"],
  weight: ["800"],
  variable: "--font-sans",
});

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased grid-background`}
      >
        <Providers>
          <div className="flex items-center justify-center min-h-svh">
            <div className="flex flex-col items-center justify-center gap-4">
              {children}
            </div>
          </div>
        </Providers>
      </body>
    </html>
  );
}
