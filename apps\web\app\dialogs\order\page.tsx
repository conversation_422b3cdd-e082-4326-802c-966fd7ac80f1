import { OrderConfirmationDialog } from "@workspace/ui/layout";
import avatar from "./avatar.png";
import activity from "./activity.jpg";

export default async function DialogDemo() {
  return (
    <OrderConfirmationDialog
      open={true}
      title="Siparişini Onayla"
      unitType="Haku-chan'dan 3 saatlik sipariş vermek üzeresin."
      boostText="Boost x3"
      unitCount={3}
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Volarant - Boost",
        value: "52",
      }}
      additionalItems={[
        { name: "Maid Cosplay", value: "10" },
        { name: "<PERSON><PERSON><PERSON>ı<PERSON>", value: "2" },
        { name: "<PERSON><PERSON> Selfie", value: "1" },
        { name: "Jim Carrey Taklidi", value: "15" },
      ]}
      total={{
        name: "Toplam",
        value: "240 soda",
      }}
      submitButtonText="GÖNDER"
      supportButtonAriaLabel="SÜRECİ GÖSTER"
    />
  );
}
