import { OrderConfirmationDialog } from "@workspace/ui/layout";
import avatar from "./avatar.png";
import activity from "./activity.jpg";

export default async function DialogDemo() {
  return (
    <OrderConfirmationDialog
      open={true}
      title="Siparişini Onayla"
      unitType="Haku-chan'dan 3 saatlik sipariş vermek üzeresin."
      boostText="Boost x3"
      unitCount={3}
      avatar={{
        src: avatar.src,
        alt: "avatar",
        width: 90,
        height: 90,
      }}
      activity={{
        src: activity.src,
        alt: "activity",
        width: 30,
        height: 30,
      }}
      mainItem={{
        name: "Volarant - Boost",
        value: "52 soda x3",
      }}
      additionalItems={[
        { name: "Maid Cosplay", value: "10 soda x3" },
        { name: "<PERSON><PERSON>k Yayın", value: "2 soda x3" },
        { name: "<PERSON><PERSON>ie", value: "1 soda x3" },
        { name: "<PERSON>", value: "15 soda x3" },
      ]}
      total={{
        name: "Toplam",
        value: "240 soda",
      }}
      submitButtonText="GÖNDER"
      supportButtonAriaLabel="SÜRECİ GÖSTER"
    />
  );
}
