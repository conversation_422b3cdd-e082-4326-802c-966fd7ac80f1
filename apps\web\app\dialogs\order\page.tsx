import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Separator } from "@workspace/ui/components/separator";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import Image from "next/image";
import avatar from "./avatar.png";
import activity from "./activity.jpg";
import { Info, LifeBuoy, ShieldQuestion } from "lucide-react";

export default async function DialogDemo() {
  return (
    <Dialog open>
      <form>
        <DialogContent className="sm:max-w-[512px]">
          <DialogHeader>
            <DialogTitle>{"Siparişini Onayla"}</DialogTitle>
            <DialogDescription className="flex gap-5">
              <span className="flex shrink-0 items-start relative">
                <span className="border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2">
                  <Image src={avatar.src} alt="avatar" width="90" height="90" />
                </span>
              </span>
              <span className="flex flex-col justify-between gap-2 pr-1">
                <span  className="pt-2.5 pr-1">
                  {"Haku-chan'dan 3 saatlik sipariş vermek üzeresin."}
                </span>
                <span className="flex justify-end items-center gap-2.5 text-foreground">
                  {"Boost x3"}
                  <Image
                    src={activity.src}
                    alt="activity"
                    width="30"
                    height="30"
                    className="rounded-sm border-2 border-background ring-foreground ring-2"
                  />
                </span>
              </span>
            </DialogDescription>
          </DialogHeader>

          <section
            id="summary"
            className="[--ring:var(--background)] fake-stroke rounded-lg bg-muted text-sm text-muted-foreground mx-7 py-5 pl-5"
          >
            <div className="overflow-y-auto max-h-44 space-y-3 pr-5">
              <ul className="">
                <li className="flex justify-between items-center">
                  <span>{"Volarant - Boost"}</span>
                  <span>{"52 soda x3"}</span>
                </li>
              </ul>
              <Separator />
              <ul className="space-y-1">
                <li className="flex justify-between">
                  <span>{"Maid Cosplay"}</span>
                  <span>{"10 soda x3"}</span>
                </li>
                <li className="flex justify-between">
                  <span>{"Ortak Yayın"}</span>
                  <span>{"2 soda x3"}</span>
                </li>
                <li className="flex justify-between">
                  <span>{"Özel Selfie"}</span>
                  <span>{"1 soda x3"}</span>
                </li>
                <li className="flex justify-between">
                  <span>{"Jim Carrey Taklidi"}</span>
                  <span>{"15 soda x3"}</span>
                </li>
              </ul>
            </div>
            <div className="space-y-2 pr-5">
              <Separator />
              <ul className="text-foreground text-lg">
                <li className="flex justify-between">
                  <span>{"Toplam"}</span>
                  <span>{"240 soda"}</span>
                </li>
              </ul>
            </div>
          </section>

          <DialogFooter>
            <Button size={"icon"}>
              <LifeBuoy className="size-9" />
              <p className="sr-only">{"SÜRECİ GÖSTER"}</p>
            </Button>
            <DialogClose asChild>
              <Button variant={"primary"}>{"GÖNDER"}</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
