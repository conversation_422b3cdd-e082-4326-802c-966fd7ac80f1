import { OrderConfirmationDialog } from "@workspace/ui/layout";
import avatar from "./avatar.png";
import activity from "./activity.jpg";

export default async function DialogDemo() {
  return (
    <OrderConfirmationDialog
      open={true}
      title="Siparişini Onayla"
      username="<PERSON><PERSON>-chan"
      unitCount={3}
      unitType="saatlik"
      serviceTitle="Boost"
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Volarant - Boost",
        sodaAmount: "52",
      }}
      serviceModifiers={[
        { name: "Maid Cosplay", sodaAmount: "10" },
        { name: "<PERSON><PERSON><PERSON>", sodaAmount: "2" },
        { name: "<PERSON><PERSON>", sodaAmount: "1" },
        { name: "<PERSON> Taklidi", sodaAmount: "15" },
      ]}
      total={{
        name: "Toplam",
        sodaAmount: "240",
      }}
      submitButtonText="GÖNDER"
      supportButtonAriaLabel="SÜRECİ GÖSTER"
    />
  );
}
